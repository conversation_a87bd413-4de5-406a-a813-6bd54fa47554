# Zenera Pages List

Danh sách tất cả các pages hiện có trong Zenera webapp và cách truy cập.

## Buyer Pages (Frontend)

### 🏠 Main Pages
- **Homepage**: `/` hoặc `/en` hoặc `/vi`
- **Products Listing**: `/products`
- **Product Detail**: `/products/[slug]` (ví dụ: `/products/iphone-15-pro-max`)
- **Categories**: `/categories/[id]` (ví dụ: `/categories/electronics`)
- **Search**: `/search?q=keyword`
- **Shops Listing**: `/shops`
- **Shop Detail**: `/shops/[slug]` (ví dụ: `/shops/apple-store`)
- **Shop Products**: `/shops/[slug]/products`

### 🛒 Shopping Pages
- **Cart**: `/cart`
- **Checkout**: `/checkout`
- **Wishlist**: `/wishlist`

### 👤 User Pages
- **Account Dashboard**: `/account`
- **Orders**: `/orders`
- **Order Detail**: `/orders/[id]` (ví dụ: `/orders/ORD-001`)

### 🎨 Demo Pages
- **Styles Demo**: `/styles-demo` - Hiển thị các components và styles

## URL Structure

### Internationalization
Tất cả các routes đều hỗ trợ đa ngôn ngữ:
- Tiếng Anh: `/en/[route]`
- Tiếng Việt: `/vi/[route]`
- Mặc định: `/[route]` (fallback to default locale)

### Examples
```
Homepage:
- /
- /en
- /vi

Product Detail:
- /products/wireless-headphones
- /en/products/wireless-headphones
- /vi/products/wireless-headphones

Cart:
- /cart
- /en/cart
- /vi/cart
```

## Development URLs

Khi chạy development server (`pnpm dev`):
- Base URL: `http://localhost:3000`
- Ví dụ: `http://localhost:3000/en/products`

## Page Status

### ✅ Completed Pages
- Homepage
- Products Listing
- Product Detail
- Categories
- Search
- Cart
- Checkout
- Account
- Orders
- Order Detail
- Wishlist
- Shops
- Shop Detail
- Shop Products
- Styles Demo

### 🚧 In Development
- (Tất cả pages cơ bản đã hoàn thành)

### 📋 Planned
- Admin Dashboard (seller pages)
- Advanced filtering
- Payment integration
- Real-time notifications

## Navigation

### Main Navigation
- Home → `/`
- Products → `/products`
- Categories → `/categories`
- Shops → `/shops`

### User Navigation
- Cart → `/cart`
- Wishlist → `/wishlist`
- Account → `/account`
- Orders → `/orders`

### Footer Links
- About Us
- Contact
- Terms of Service
- Privacy Policy

## Testing Pages

Để test các pages:

1. **Start development server**:
   ```bash
   cd Zenera/packages/webapp
   pnpm dev
   ```

2. **Access pages**:
   - Open browser: `http://localhost:3000`
   - Navigate to any route listed above

3. **Test internationalization**:
   - Add `/en` or `/vi` prefix to any route
   - Check language switching functionality

## Notes

- Tất cả pages đều responsive (mobile, tablet, desktop)
- Hỗ trợ dark/light mode (nếu được implement)
- SEO optimized với proper meta tags
- Accessibility compliant (WCAG guidelines)
