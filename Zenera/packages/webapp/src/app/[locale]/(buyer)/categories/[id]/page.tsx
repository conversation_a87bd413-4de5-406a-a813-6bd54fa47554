import type { Metadata } from 'next';
import { CategoryDetailPage } from '@/components/buyer/categories/category-detail-page';

interface PageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
  searchParams: Promise<{
    page?: string;
    sort?: string;
    filter?: string;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale, id } = await params;
  
  return {
    title: 'Category - Zenera',
    description: 'Browse products in this category. Find exactly what you\'re looking for with our organized product categories.',
    openGraph: {
      title: 'Category - Zenera',
      description: 'Browse products in this category. Find exactly what you\'re looking for with our organized product categories.',
      type: 'website',
    },
  };
}

export default async function CategoryDetailPageRoute({ params, searchParams }: PageProps) {
  const { locale, id } = await params;
  const { page = '1', sort = 'newest', filter } = await searchParams;
  
  return (
    <CategoryDetailPage 
      locale={locale} 
      categoryId={id}
      page={parseInt(page)}
      sort={sort}
      filter={filter}
    />
  );
}
