import type { Metadata } from 'next';
import { SearchPage } from '@/components/buyer/search/search-page';

interface PageProps {
  params: Promise<{
    locale: string;
  }>;
  searchParams: Promise<{
    q?: string;
    category?: string;
    page?: string;
    sort?: string;
    minPrice?: string;
    maxPrice?: string;
    brand?: string;
    rating?: string;
  }>;
}

export async function generateMetadata({ params, searchParams }: PageProps): Promise<Metadata> {
  const { locale } = await params;
  const { q } = await searchParams;
  
  const title = q ? `Search results for "${q}" - Zenera` : 'Search - Zenera';
  const description = q 
    ? `Find products matching "${q}". Browse through our extensive catalog with advanced search filters.`
    : 'Search for products across our entire catalog. Use filters to find exactly what you need.';
  
  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
  };
}

export default async function SearchPageRoute({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const { 
    q = '', 
    category, 
    page = '1', 
    sort = 'relevance',
    minPrice,
    maxPrice,
    brand,
    rating
  } = await searchParams;
  
  return (
    <SearchPage 
      locale={locale} 
      query={q}
      category={category}
      page={parseInt(page)}
      sort={sort}
      minPrice={minPrice ? parseFloat(minPrice) : undefined}
      maxPrice={maxPrice ? parseFloat(maxPrice) : undefined}
      brand={brand}
      rating={rating}
    />
  );
}
