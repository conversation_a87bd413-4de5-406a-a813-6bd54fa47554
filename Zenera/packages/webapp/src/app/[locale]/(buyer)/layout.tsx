import type { ReactNode } from 'react';
import { Navbar } from '@/components/buyer/layout/navbar';
import { Footer } from '@/components/buyer/layout/footer';

interface BuyerLayoutProps {
  children: ReactNode;
  params: Promise<{
    locale: string;
  }>;
}

export default async function BuyerLayout({ children, params }: BuyerLayoutProps) {
  const { locale } = await params;

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Navbar locale={locale} />
      <main className="flex-grow">{children}</main>
      <Footer locale={locale} />
    </div>
  );
}
