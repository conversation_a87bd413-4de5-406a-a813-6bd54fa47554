"use client";

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Category } from '@zenera/sharing';
import { Filter, X, Star } from 'lucide-react';

interface SearchFiltersProps {
  locale: string;
  categories: Category[];
  currentCategory?: string;
  currentSort: string;
  currentMinPrice?: number;
  currentMaxPrice?: number;
  currentBrand?: string;
  currentRating?: string;
}

const sortOptions = [
  { value: 'relevance', label: 'Most Relevant' },
  { value: 'newest', label: 'Newest First' },
  { value: 'price-low', label: 'Price: Low to High' },
  { value: 'price-high', label: 'Price: High to Low' },
  { value: 'rating', label: 'Highest Rated' },
  { value: 'popular', label: 'Most Popular' }
];

const brandOptions = [
  { value: 'apple', label: 'Apple', count: 45 },
  { value: 'samsung', label: 'Samsung', count: 32 },
  { value: 'sony', label: 'Sony', count: 28 },
  { value: 'lg', label: 'LG', count: 19 },
  { value: 'dell', label: 'Dell', count: 15 },
  { value: 'hp', label: 'HP', count: 12 }
];

const ratingOptions = [
  { value: '5', label: '5 Stars', count: 120 },
  { value: '4', label: '4 Stars & Up', count: 340 },
  { value: '3', label: '3 Stars & Up', count: 580 },
  { value: '2', label: '2 Stars & Up', count: 720 }
];

export function SearchFilters({ 
  locale, 
  categories,
  currentCategory,
  currentSort,
  currentMinPrice = 0,
  currentMaxPrice = 5000,
  currentBrand,
  currentRating
}: SearchFiltersProps) {
  const { t } = useZeneraTranslation('search');
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [priceRange, setPriceRange] = useState([currentMinPrice, currentMaxPrice]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>(
    currentBrand ? currentBrand.split(',') : []
  );

  const updateURL = (newParams: Record<string, string | undefined>) => {
    const params = new URLSearchParams(searchParams);
    
    Object.entries(newParams).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });

    router.push(`?${params.toString()}`);
  };

  const handleSortChange = (sort: string) => {
    updateURL({ sort });
  };

  const handleCategoryChange = (categoryId: string) => {
    updateURL({ 
      category: currentCategory === categoryId ? undefined : categoryId,
      page: undefined // Reset page when changing category
    });
  };

  const handleBrandChange = (brand: string, checked: boolean) => {
    const newBrands = checked 
      ? [...selectedBrands, brand]
      : selectedBrands.filter(b => b !== brand);
    
    setSelectedBrands(newBrands);
    updateURL({ 
      brand: newBrands.length > 0 ? newBrands.join(',') : undefined,
      page: undefined
    });
  };

  const handleRatingChange = (rating: string) => {
    const newRating = currentRating === rating ? undefined : rating;
    updateURL({ 
      rating: newRating,
      page: undefined
    });
  };

  const handlePriceChange = (values: number[]) => {
    setPriceRange(values);
    updateURL({ 
      minPrice: values[0] > 0 ? values[0].toString() : undefined,
      maxPrice: values[1] < 5000 ? values[1].toString() : undefined,
      page: undefined
    });
  };

  const clearAllFilters = () => {
    setPriceRange([0, 5000]);
    setSelectedBrands([]);
    const params = new URLSearchParams(searchParams);
    const query = params.get('q');
    
    // Keep only the search query
    const newParams = new URLSearchParams();
    if (query) {
      newParams.set('q', query);
    }
    
    router.push(`?${newParams.toString()}`);
  };

  const activeFiltersCount = 
    (currentCategory ? 1 : 0) +
    selectedBrands.length + 
    (currentRating ? 1 : 0) + 
    (currentMinPrice > 0 || currentMaxPrice < 5000 ? 1 : 0);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="space-y-6">
      {/* Filter Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Filter className="w-5 h-5" />
              {t('filters')}
            </CardTitle>
            {activeFiltersCount > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {activeFiltersCount}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="w-4 h-4 mr-1" />
                  {t('clear')}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Sort Options */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">{t('sortBy')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {sortOptions.map((option) => (
            <Button
              key={option.value}
              variant={currentSort === option.value ? "default" : "ghost"}
              size="sm"
              className="w-full justify-start"
              onClick={() => handleSortChange(option.value)}
            >
              {option.label}
            </Button>
          ))}
        </CardContent>
      </Card>

      {/* Categories */}
      {categories.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">{t('categories')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={currentCategory === category.id ? "default" : "ghost"}
                size="sm"
                className="w-full justify-start"
                onClick={() => handleCategoryChange(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Price Range */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">{t('priceRange')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Slider
              value={priceRange}
              onValueChange={handlePriceChange}
              max={5000}
              step={50}
              className="w-full"
            />
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>${priceRange[0]}</span>
              <span>${priceRange[1]}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Brands */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">{t('brands')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {brandOptions.map((brand) => (
            <div key={brand.value} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={brand.value}
                  checked={selectedBrands.includes(brand.value)}
                  onCheckedChange={(checked) => 
                    handleBrandChange(brand.value, checked as boolean)
                  }
                />
                <label
                  htmlFor={brand.value}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {brand.label}
                </label>
              </div>
              <span className="text-xs text-gray-500">({brand.count})</span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Rating */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">{t('rating')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {ratingOptions.map((rating) => (
            <Button
              key={rating.value}
              variant={currentRating === rating.value ? "default" : "ghost"}
              size="sm"
              className="w-full justify-between"
              onClick={() => handleRatingChange(rating.value)}
            >
              <div className="flex items-center gap-1">
                {renderStars(parseInt(rating.value))}
                <span className="ml-2">{rating.label}</span>
              </div>
              <span className="text-xs">({rating.count})</span>
            </Button>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}
