"use client";

import { useState } from 'react';
import Link from 'next/link';
import { Product } from '@zenera/sharing';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Grid3X3, 
  List, 
  Star, 
  ShoppingCart, 
  Heart,
  ChevronLeft,
  ChevronRight,
  Search
} from 'lucide-react';
import { useCartStore } from '@/stores/cart-store';

interface SearchResultsProps {
  results: Product[];
  totalResults: number;
  currentPage: number;
  query: string;
  locale: string;
  sort: string;
}

export function SearchResults({ 
  results, 
  totalResults, 
  currentPage, 
  query,
  locale, 
  sort 
}: SearchResultsProps) {
  const { t } = useZeneraTranslation('search');
  const { addItem } = useCartStore();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  
  const resultsPerPage = 12;
  const totalPages = Math.ceil(totalResults / resultsPerPage);

  const handleAddToCart = (product: Product) => {
    addItem(product, undefined, 1);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const highlightSearchTerm = (text: string, searchTerm: string) => {
    if (!searchTerm) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  const ProductCard = ({ product }: { product: Product }) => (
    <Card className="group h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
      <CardContent className="p-0">
        <div className="relative">
          <Link href={`/${locale}/products/${product.slug}`}>
            <div className="aspect-square overflow-hidden rounded-t-lg bg-gray-100">
              <img
                src={product.images?.[0] || '/placeholder-product.jpg'}
                alt={product.name}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>
          </Link>
          
          {/* Wishlist Button */}
          <Button
            size="sm"
            variant="ghost"
            className="absolute top-2 right-2 w-8 h-8 p-0 bg-white/80 backdrop-blur-sm hover:bg-white"
          >
            <Heart className="w-4 h-4" />
          </Button>

          {/* Sale Badge */}
          {product.compare_at_price && product.base_price && product.compare_at_price > product.base_price && (
            <Badge className="absolute top-2 left-2 bg-red-500 text-white">
              {Math.round(((product.compare_at_price - product.base_price) / product.compare_at_price) * 100)}% OFF
            </Badge>
          )}
        </div>

        <div className="p-4">
          <Link href={`/${locale}/products/${product.slug}`}>
            <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {highlightSearchTerm(product.name, query)}
            </h3>
          </Link>

          {/* Rating */}
          <div className="flex items-center gap-2 mb-2">
            <div className="flex items-center">
              {renderStars(product.avg_rating || 0)}
            </div>
            <span className="text-sm text-gray-600">
              ({product.review_count || 0})
            </span>
          </div>

          {/* Price */}
          <div className="flex items-center gap-2 mb-3">
            <span className="text-lg font-bold text-gray-900">
              {formatPrice(product.base_price || 0)}
            </span>
            {product.compare_at_price && product.base_price && product.compare_at_price > product.base_price && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(product.compare_at_price)}
              </span>
            )}
          </div>

          {/* Add to Cart Button */}
          <Button
            onClick={() => handleAddToCart(product)}
            className="w-full"
            size="sm"
          >
            <ShoppingCart className="w-4 h-4 mr-2" />
            {t('addToCart')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const ProductListItem = ({ product }: { product: Product }) => (
    <Card className="group transition-all duration-300 hover:shadow-md">
      <CardContent className="p-4">
        <div className="flex gap-4">
          <Link href={`/${locale}/products/${product.slug}`} className="flex-shrink-0">
            <div className="w-24 h-24 rounded-lg overflow-hidden bg-gray-100">
              <img
                src={product.images?.[0] || '/placeholder-product.jpg'}
                alt={product.name}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>
          </Link>

          <div className="flex-grow">
            <Link href={`/${locale}/products/${product.slug}`}>
              <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                {highlightSearchTerm(product.name, query)}
              </h3>
            </Link>

            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
              {highlightSearchTerm(product.description, query)}
            </p>

            <div className="flex items-center gap-2 mb-2">
              <div className="flex items-center">
                {renderStars(product.avg_rating || 0)}
              </div>
              <span className="text-sm text-gray-600">
                ({product.review_count || 0})
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold text-gray-900">
                  {formatPrice(product.base_price || 0)}
                </span>
                {product.compare_at_price && product.base_price && product.compare_at_price > product.base_price && (
                  <span className="text-sm text-gray-500 line-through">
                    {formatPrice(product.compare_at_price)}
                  </span>
                )}
              </div>

              <Button
                onClick={() => handleAddToCart(product)}
                size="sm"
              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                {t('addToCart')}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (totalResults === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <Search className="w-12 h-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('noResults')}
        </h3>
        <p className="text-gray-600">
          {t('noResultsDescription')}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <p className="text-sm text-gray-600">
            {t('showing')} {(currentPage - 1) * resultsPerPage + 1}-{Math.min(currentPage * resultsPerPage, totalResults)} {t('of')} {totalResults} {t('results')}
          </p>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Results */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {results.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {results.map((product) => (
            <ProductListItem key={product.id} product={product} />
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 mt-8">
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage === 1}
          >
            <ChevronLeft className="w-4 h-4" />
            {t('previous')}
          </Button>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const page = i + 1;
            return (
              <Button
                key={page}
                variant={currentPage === page ? 'default' : 'outline'}
                size="sm"
              >
                {page}
              </Button>
            );
          })}
          
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage === totalPages}
          >
            {t('next')}
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
