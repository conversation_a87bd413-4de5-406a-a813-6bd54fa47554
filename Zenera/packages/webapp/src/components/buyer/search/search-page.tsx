"use client";

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { SearchHeader } from './search-header';
import { SearchFilters } from './search-filters';
import { SearchResults } from './search-results';
import { SearchSuggestions } from './search-suggestions';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Product, Category } from '@zenera/sharing';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface SearchPageProps {
  locale: string;
  query: string;
  category?: string;
  page: number;
  sort: string;
  minPrice?: number;
  maxPrice?: number;
  brand?: string;
  rating?: string;
}

// Mock data - sẽ thay thế bằng API call thực tế
const mockSearchResults: Product[] = [
  {
    id: 'prod-1',
    name: 'iPhone 15 Pro Max',
    slug: 'iphone-15-pro-max',
    description: 'Latest iPhone with advanced camera system and A17 Pro chip',
    base_price: 1199.99,
    compare_at_price: 1299.99,
    images: ['https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop'],
    category_id: 'cat-1-1',
    is_active: true,
    tags: ['smartphone', 'apple', 'premium'],
    attributes: [],
    avg_rating: 4.8,
    review_count: 1250,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-15')
  },
  {
    id: 'prod-2',
    name: 'Samsung Galaxy S24 Ultra',
    slug: 'samsung-galaxy-s24-ultra',
    description: 'Flagship Android phone with S Pen and advanced AI features',
    base_price: 1099.99,
    compare_at_price: 1199.99,
    images: ['https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400&h=400&fit=crop'],
    category_id: 'cat-1-1',
    is_active: true,
    tags: ['smartphone', 'samsung', 'android'],
    attributes: [],
    avg_rating: 4.7,
    review_count: 890,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-15')
  },
  {
    id: 'prod-3',
    name: 'MacBook Pro 16"',
    slug: 'macbook-pro-16',
    description: 'Powerful laptop with M3 Pro chip for professional work',
    base_price: 2499.99,
    compare_at_price: 2699.99,
    images: ['https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=400&fit=crop'],
    category_id: 'cat-1-2',
    is_active: true,
    tags: ['laptop', 'apple', 'professional'],
    attributes: [],
    avg_rating: 4.9,
    review_count: 890,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-15')
  }
];

const mockCategories: Category[] = [
  {
    id: 'cat-1',
    name: 'Electronics',
    slug: 'electronics',
    description: 'Latest electronic devices and gadgets',
    is_active: true,
    sort_order: 1
  },
  {
    id: 'cat-2',
    name: 'Fashion',
    slug: 'fashion',
    description: 'Trendy clothing and accessories',
    is_active: true,
    sort_order: 2
  },
  {
    id: 'cat-3',
    name: 'Home & Garden',
    slug: 'home-garden',
    description: 'Everything for your home and garden',
    is_active: true,
    sort_order: 3
  }
];

const mockSuggestions = [
  'iPhone 15',
  'Samsung Galaxy',
  'MacBook Pro',
  'AirPods',
  'iPad',
  'Apple Watch',
  'Gaming Laptop',
  'Wireless Headphones'
];

export function SearchPage({ 
  locale, 
  query, 
  category,
  page, 
  sort,
  minPrice,
  maxPrice,
  brand,
  rating
}: SearchPageProps) {
  const { t } = useZeneraTranslation('search');
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<Product[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    const fetchSearchResults = async () => {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Filter results based on query
      let filteredResults = mockSearchResults;
      
      if (query) {
        filteredResults = mockSearchResults.filter(product =>
          product.name.toLowerCase().includes(query.toLowerCase()) ||
          product.description.toLowerCase().includes(query.toLowerCase()) ||
          product.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
        );
      }
      
      // Apply filters
      if (category) {
        filteredResults = filteredResults.filter(product => product.category_id === category);
      }
      
      if (minPrice !== undefined) {
        filteredResults = filteredResults.filter(product => (product.base_price || 0) >= minPrice);
      }

      if (maxPrice !== undefined) {
        filteredResults = filteredResults.filter(product => (product.base_price || 0) <= maxPrice);
      }

      if (rating) {
        const minRating = parseFloat(rating);
        filteredResults = filteredResults.filter(product => (product.avg_rating || 0) >= minRating);
      }

      // Sort results
      switch (sort) {
        case 'price-low':
          filteredResults.sort((a, b) => (a.base_price || 0) - (b.base_price || 0));
          break;
        case 'price-high':
          filteredResults.sort((a, b) => (b.base_price || 0) - (a.base_price || 0));
          break;
        case 'rating':
          filteredResults.sort((a, b) => (b.avg_rating || 0) - (a.avg_rating || 0));
          break;
        case 'newest':
          filteredResults.sort((a, b) => (b.created_at?.getTime() || 0) - (a.created_at?.getTime() || 0));
          break;
        default: // relevance
          // Keep original order for relevance
          break;
      }
      
      setResults(filteredResults);
      setTotalResults(filteredResults.length);
      setCategories(mockCategories);
      setSuggestions(mockSuggestions);
      setLoading(false);
    };

    fetchSearchResults();
  }, [query, category, page, sort, minPrice, maxPrice, brand, rating]);

  const breadcrumbItems = [
    { label: t('home'), href: `/${locale}` },
    { label: t('search'), href: `/${locale}/search` }
  ];

  if (query) {
    breadcrumbItems.push({
      label: `"${query}"`,
      href: `/${locale}/search?q=${encodeURIComponent(query)}`
    });
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <SimpleBreadcrumb items={breadcrumbItems} />
        
        <SearchHeader 
          query={query}
          totalResults={totalResults}
          locale={locale}
          loading={loading}
        />

        {!query && !loading && (
          <SearchSuggestions 
            suggestions={suggestions}
            categories={categories}
            locale={locale}
          />
        )}

        {(query || category) && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mt-8">
            <div className="lg:col-span-1">
              <SearchFilters 
                locale={locale}
                categories={categories}
                currentCategory={category}
                currentSort={sort}
                currentMinPrice={minPrice}
                currentMaxPrice={maxPrice}
                currentBrand={brand}
                currentRating={rating}
              />
            </div>
            
            <div className="lg:col-span-3">
              {loading ? (
                <div className="flex justify-center py-12">
                  <LoadingSpinner />
                </div>
              ) : (
                <SearchResults 
                  results={results}
                  totalResults={totalResults}
                  currentPage={page}
                  query={query}
                  locale={locale}
                  sort={sort}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
