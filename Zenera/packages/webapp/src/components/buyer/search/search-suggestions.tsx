"use client";

import Link from 'next/link';
import { Category } from '@zenera/sharing';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Tag, Grid3X3 } from 'lucide-react';

interface SearchSuggestionsProps {
  suggestions: string[];
  categories: Category[];
  locale: string;
}

export function SearchSuggestions({ suggestions, categories, locale }: SearchSuggestionsProps) {
  const { t } = useZeneraTranslation('search');

  return (
    <div className="mt-12 space-y-8">
      {/* Popular Searches */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-orange-500" />
            {t('popularSearches')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {suggestions.map((suggestion, index) => (
              <Link
                key={index}
                href={`/${locale}/search?q=${encodeURIComponent(suggestion)}`}
                className="group"
              >
                <Badge 
                  variant="secondary" 
                  className="cursor-pointer transition-all duration-200 hover:bg-blue-100 hover:text-blue-700 hover:border-blue-300 group-hover:scale-105"
                >
                  <Tag className="w-3 h-3 mr-1" />
                  {suggestion}
                </Badge>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Browse by Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Grid3X3 className="w-5 h-5 text-green-500" />
            {t('browseCategories')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/${locale}/categories/${category.id}`}
                className="group"
              >
                <div className="p-4 rounded-lg border border-gray-200 transition-all duration-200 hover:shadow-md hover:border-blue-300 hover:bg-blue-50 group-hover:-translate-y-1">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg flex items-center justify-center group-hover:from-blue-200 group-hover:to-indigo-200 transition-colors">
                      <span className="text-lg font-bold text-blue-600">
                        {category.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">
                        {category.name}
                      </h3>
                      {category.description && (
                        <p className="text-sm text-gray-600 line-clamp-1">
                          {category.description}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Search Tips */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-900">
            {t('searchTipsTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-semibold mb-2">{t('basicTips')}</h4>
              <ul className="space-y-1">
                <li>• {t('basicTip1')}</li>
                <li>• {t('basicTip2')}</li>
                <li>• {t('basicTip3')}</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">{t('advancedTips')}</h4>
              <ul className="space-y-1">
                <li>• {t('advancedTip1')}</li>
                <li>• {t('advancedTip2')}</li>
                <li>• {t('advancedTip3')}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
