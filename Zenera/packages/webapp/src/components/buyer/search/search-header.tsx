"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Search, X, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface SearchHeaderProps {
  query: string;
  totalResults: number;
  locale: string;
  loading: boolean;
}

export function SearchHeader({ query, totalResults, locale, loading }: SearchHeaderProps) {
  const { t } = useZeneraTranslation('search');
  const router = useRouter();
  const [searchInput, setSearchInput] = useState(query);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchInput.trim()) {
      router.push(`/${locale}/search?q=${encodeURIComponent(searchInput.trim())}`);
    }
  };

  const clearSearch = () => {
    setSearchInput('');
    router.push(`/${locale}/search`);
  };

  return (
    <div className="mt-6">
      {/* Search Form */}
      <div className="max-w-4xl mx-auto">
        <form onSubmit={handleSearch} className="relative">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder={t('searchPlaceholder')}
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="pl-12 pr-12 h-14 text-lg border-2 border-gray-200 focus:border-blue-500 rounded-xl shadow-sm"
            />
            {searchInput && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={clearSearch}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 p-0 hover:bg-gray-100"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
          <Button
            type="submit"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-10 px-6"
            disabled={loading}
          >
            {loading ? t('searching') : t('search')}
          </Button>
        </form>
      </div>

      {/* Search Results Info */}
      {query && (
        <div className="mt-8">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                {t('searchResults')}
              </h1>
              <div className="flex items-center gap-2 mt-2">
                <p className="text-gray-600">
                  {loading ? (
                    t('searching')
                  ) : (
                    <>
                      {totalResults > 0 ? (
                        <>
                          {t('found')} <span className="font-semibold">{totalResults}</span> {t('results')} {t('for')} 
                          <Badge variant="secondary" className="ml-2">
                            "{query}"
                          </Badge>
                        </>
                      ) : (
                        <>
                          {t('noResults')} {t('for')} 
                          <Badge variant="secondary" className="ml-2">
                            "{query}"
                          </Badge>
                        </>
                      )}
                    </>
                  )}
                </p>
              </div>
            </div>

            {/* Mobile Filter Toggle */}
            <div className="sm:hidden">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="w-4 h-4" />
                {t('filters')}
              </Button>
            </div>
          </div>

          {/* Search Tips for No Results */}
          {!loading && totalResults === 0 && query && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h3 className="font-semibold text-blue-900 mb-2">
                {t('searchTips')}
              </h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• {t('tip1')}</li>
                <li>• {t('tip2')}</li>
                <li>• {t('tip3')}</li>
                <li>• {t('tip4')}</li>
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Welcome Message for Empty Search */}
      {!query && !loading && (
        <div className="text-center mt-12">
          <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
            <Search className="w-12 h-12 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t('searchTitle')}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('searchDescription')}
          </p>
        </div>
      )}
    </div>
  );
}
