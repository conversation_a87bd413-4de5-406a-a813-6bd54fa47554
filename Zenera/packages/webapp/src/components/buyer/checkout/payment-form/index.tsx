"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { PaymentMethodSelector } from './payment-method-selector';
import { CardPaymentForm } from './card-payment-form';
import { AlternativePaymentMethods } from './alternative-payment-methods';

interface PaymentFormProps {
  onSubmit: (data: any) => void;
  onBack: () => void;
  locale: string;
}

interface PaymentFormData {
  method: string;
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
}

export function PaymentForm({ onSubmit, onBack, locale }: PaymentFormProps) {
  const { t } = useZeneraTranslation('checkout');
  
  const [formData, setFormData] = useState<PaymentFormData>({
    method: 'card',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof PaymentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (formData.method === 'card') {
      if (!formData.cardNumber?.replace(/\s/g, '')) {
        newErrors.cardNumber = t('cardNumberRequired');
      } else if (formData.cardNumber.replace(/\s/g, '').length < 13) {
        newErrors.cardNumber = t('cardNumberInvalid');
      }
      
      if (!formData.expiryDate) {
        newErrors.expiryDate = t('expiryDateRequired');
      } else if (!/^\d{2}\/\d{2}$/.test(formData.expiryDate)) {
        newErrors.expiryDate = t('expiryDateInvalid');
      }
      
      if (!formData.cvv) {
        newErrors.cvv = t('cvvRequired');
      } else if (formData.cvv.length < 3) {
        newErrors.cvv = t('cvvInvalid');
      }
      
      if (!formData.cardholderName?.trim()) {
        newErrors.cardholderName = t('cardholderNameRequired');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      onSubmit(formData);
    } catch (error) {
      console.error('Payment form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Payment Method Selection */}
      <PaymentMethodSelector
        selectedMethod={formData.method}
        onMethodChange={(method) => handleInputChange('method', method)}
      />

      {/* Payment Details */}
      {formData.method === 'card' && (
        <CardPaymentForm
          formData={formData}
          errors={errors}
          onInputChange={handleInputChange}
        />
      )}

      {(formData.method === 'paypal' || formData.method === 'bank') && (
        <AlternativePaymentMethods method={formData.method} />
      )}

      {/* Form Actions */}
      <div className="flex gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          {t('backToShipping')}
        </Button>
        
        <Button
          type="submit"
          disabled={isSubmitting}
          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isSubmitting ? t('processing') : t('reviewOrder')}
        </Button>
      </div>
    </form>
  );
}
