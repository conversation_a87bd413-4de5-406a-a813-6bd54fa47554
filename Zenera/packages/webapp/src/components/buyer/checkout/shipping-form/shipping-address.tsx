"use client";

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MapPin } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface ShippingAddressProps {
  formData: {
    address: string;
    apartment?: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  errors: Record<string, string>;
  onInputChange: (field: 'address' | 'apartment' | 'city' | 'state' | 'zipCode' | 'country', value: string) => void;
}

const countries = [
  { value: 'US', label: 'United States' },
  { value: 'VN', label: 'Vietnam' },
  { value: 'CA', label: 'Canada' },
  { value: 'GB', label: 'United Kingdom' },
  { value: 'AU', label: 'Australia' },
];

export function ShippingAddress({ formData, errors, onInputChange }: ShippingAddressProps) {
  const { t } = useZeneraTranslation('checkout');

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium flex items-center gap-2">
        <MapPin className="w-5 h-5" />
        {t('shippingAddress')}
      </h3>

      <div>
        <Label htmlFor="address">{t('address')} *</Label>
        <Input
          id="address"
          value={formData.address}
          onChange={(e) => onInputChange('address', e.target.value)}
          className={errors.address ? 'border-red-500' : ''}
          placeholder={t('addressPlaceholder')}
        />
        {errors.address && (
          <p className="text-sm text-red-600 mt-1">{errors.address}</p>
        )}
      </div>

      <div>
        <Label htmlFor="apartment">{t('apartment')}</Label>
        <Input
          id="apartment"
          value={formData.apartment}
          onChange={(e) => onInputChange('apartment', e.target.value)}
          placeholder={t('apartmentPlaceholder')}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="city">{t('city')} *</Label>
          <Input
            id="city"
            value={formData.city}
            onChange={(e) => onInputChange('city', e.target.value)}
            className={errors.city ? 'border-red-500' : ''}
          />
          {errors.city && (
            <p className="text-sm text-red-600 mt-1">{errors.city}</p>
          )}
        </div>

        <div>
          <Label htmlFor="state">{t('state')} *</Label>
          <Input
            id="state"
            value={formData.state}
            onChange={(e) => onInputChange('state', e.target.value)}
            className={errors.state ? 'border-red-500' : ''}
          />
          {errors.state && (
            <p className="text-sm text-red-600 mt-1">{errors.state}</p>
          )}
        </div>

        <div>
          <Label htmlFor="zipCode">{t('zipCode')} *</Label>
          <Input
            id="zipCode"
            value={formData.zipCode}
            onChange={(e) => onInputChange('zipCode', e.target.value)}
            className={errors.zipCode ? 'border-red-500' : ''}
          />
          {errors.zipCode && (
            <p className="text-sm text-red-600 mt-1">{errors.zipCode}</p>
          )}
        </div>
      </div>

      <div>
        <Label htmlFor="country">{t('country')} *</Label>
        <Select value={formData.country} onValueChange={(value) => onInputChange('country', value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {countries.map((country) => (
              <SelectItem key={country.value} value={country.value}>
                {country.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
