"use client";

import { useState, useEffect } from 'react';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { CategoryHeader } from './category-header';
import { CategoryFilters } from './category-filters';
import { CategoryProducts } from './category-products';
import { CategorySubcategories } from './category-subcategories';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Category, Product } from '@zenera/sharing';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface CategoryDetailPageProps {
  locale: string;
  categoryId: string;
  page: number;
  sort: string;
  filter?: string;
}

// Mock data - sẽ thay thế bằng API call thực tế
const mockCategory: Category = {
  id: 'cat-1',
  name: 'Electronics',
  slug: 'electronics',
  description: 'Latest electronic devices and gadgets for modern life',
  image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=800&h=400&fit=crop',
  is_active: true,
  sort_order: 1,
  children: [
    {
      id: 'cat-1-1',
      name: 'Smartphones',
      slug: 'smartphones',
      description: 'Latest smartphones and mobile devices',
      image: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=200&fit=crop',
      parent_id: 'cat-1',
      is_active: true,
      sort_order: 1
    },
    {
      id: 'cat-1-2', 
      name: 'Laptops',
      slug: 'laptops',
      description: 'High-performance laptops for work and gaming',
      image: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=300&h=200&fit=crop',
      parent_id: 'cat-1',
      is_active: true,
      sort_order: 2
    },
    {
      id: 'cat-1-3',
      name: 'Headphones',
      slug: 'headphones', 
      description: 'Premium audio equipment and headphones',
      image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=200&fit=crop',
      parent_id: 'cat-1',
      is_active: true,
      sort_order: 3
    }
  ],
  created_at: new Date('2024-01-01'),
  updated_at: new Date('2024-01-15')
};

const mockProducts: Product[] = [
  {
    id: 'prod-1',
    name: 'iPhone 15 Pro Max',
    slug: 'iphone-15-pro-max',
    description: 'Latest iPhone with advanced camera system and A17 Pro chip',
    price: 1199.99,
    compare_price: 1299.99,
    images: ['https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop'],
    category_id: 'cat-1-1',
    shop_id: 'shop-1',
    is_active: true,
    stock_quantity: 50,
    sku: 'IPH15PM-256-TIT',
    weight: 0.221,
    dimensions: { length: 15.99, width: 7.69, height: 0.83 },
    tags: ['smartphone', 'apple', 'premium'],
    rating: 4.8,
    review_count: 1250,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-15')
  },
  {
    id: 'prod-2',
    name: 'MacBook Pro 16"',
    slug: 'macbook-pro-16',
    description: 'Powerful laptop with M3 Pro chip for professional work',
    price: 2499.99,
    compare_price: 2699.99,
    images: ['https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=400&fit=crop'],
    category_id: 'cat-1-2',
    shop_id: 'shop-1',
    is_active: true,
    stock_quantity: 25,
    sku: 'MBP16-M3P-512-SG',
    weight: 2.15,
    dimensions: { length: 35.57, width: 24.81, height: 1.68 },
    tags: ['laptop', 'apple', 'professional'],
    rating: 4.9,
    review_count: 890,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-15')
  },
  {
    id: 'prod-3',
    name: 'Sony WH-1000XM5',
    slug: 'sony-wh-1000xm5',
    description: 'Industry-leading noise canceling headphones',
    price: 399.99,
    compare_price: 449.99,
    images: ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop'],
    category_id: 'cat-1-3',
    shop_id: 'shop-2',
    is_active: true,
    stock_quantity: 75,
    sku: 'SONY-WH1000XM5-BLK',
    weight: 0.25,
    dimensions: { length: 25.4, width: 21.4, height: 8.9 },
    tags: ['headphones', 'sony', 'noise-canceling'],
    rating: 4.7,
    review_count: 2100,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-15')
  }
];

export function CategoryDetailPage({ 
  locale, 
  categoryId, 
  page, 
  sort, 
  filter 
}: CategoryDetailPageProps) {
  const { t } = useZeneraTranslation('categories');
  const [loading, setLoading] = useState(true);
  const [category, setCategory] = useState<Category | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [totalProducts, setTotalProducts] = useState(0);

  useEffect(() => {
    // Simulate API call
    const fetchCategoryData = async () => {
      setLoading(true);
      
      // Simulate loading delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setCategory(mockCategory);
      setProducts(mockProducts);
      setTotalProducts(mockProducts.length);
      setLoading(false);
    };

    fetchCategoryData();
  }, [categoryId, page, sort, filter]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (!category) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {t('notFound')}
          </h1>
          <p className="text-gray-600">
            {t('categoryNotFoundDescription')}
          </p>
        </div>
      </div>
    );
  }

  const breadcrumbItems = [
    { label: t('home'), href: `/${locale}` },
    { label: t('categories'), href: `/${locale}/categories` },
    { label: category.name, href: `/${locale}/categories/${category.id}` }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <SimpleBreadcrumb items={breadcrumbItems} />
        
        <CategoryHeader 
          category={category}
          totalProducts={totalProducts}
          locale={locale}
        />

        {category.children && category.children.length > 0 && (
          <CategorySubcategories 
            subcategories={category.children}
            locale={locale}
          />
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mt-8">
          <div className="lg:col-span-1">
            <CategoryFilters 
              locale={locale}
              currentSort={sort}
              currentFilter={filter}
            />
          </div>
          
          <div className="lg:col-span-3">
            <CategoryProducts 
              products={products}
              totalProducts={totalProducts}
              currentPage={page}
              locale={locale}
              sort={sort}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
