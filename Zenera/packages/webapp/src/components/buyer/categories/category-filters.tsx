"use client";

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Filter, X, Star } from 'lucide-react';

interface CategoryFiltersProps {
  locale: string;
  currentSort: string;
  currentFilter?: string;
}

const sortOptions = [
  { value: 'newest', label: 'Newest First' },
  { value: 'oldest', label: 'Oldest First' },
  { value: 'price-low', label: 'Price: Low to High' },
  { value: 'price-high', label: 'Price: High to Low' },
  { value: 'rating', label: 'Highest Rated' },
  { value: 'popular', label: 'Most Popular' }
];

const brandOptions = [
  { value: 'apple', label: 'Apple', count: 45 },
  { value: 'samsung', label: 'Samsung', count: 32 },
  { value: 'sony', label: 'Sony', count: 28 },
  { value: 'lg', label: 'LG', count: 19 },
  { value: 'dell', label: 'Dell', count: 15 }
];

const ratingOptions = [
  { value: '5', label: '5 Stars', count: 120 },
  { value: '4', label: '4 Stars & Up', count: 340 },
  { value: '3', label: '3 Stars & Up', count: 580 },
  { value: '2', label: '2 Stars & Up', count: 720 }
];

export function CategoryFilters({ locale, currentSort, currentFilter }: CategoryFiltersProps) {
  const { t } = useZeneraTranslation('categories');
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [priceRange, setPriceRange] = useState([0, 5000]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [selectedRating, setSelectedRating] = useState<string>('');
  const [inStock, setInStock] = useState(false);

  const updateURL = (newParams: Record<string, string | undefined>) => {
    const params = new URLSearchParams(searchParams);
    
    Object.entries(newParams).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });

    router.push(`?${params.toString()}`);
  };

  const handleSortChange = (sort: string) => {
    updateURL({ sort });
  };

  const handleBrandChange = (brand: string, checked: boolean) => {
    const newBrands = checked 
      ? [...selectedBrands, brand]
      : selectedBrands.filter(b => b !== brand);
    
    setSelectedBrands(newBrands);
    updateURL({ brands: newBrands.length > 0 ? newBrands.join(',') : undefined });
  };

  const handleRatingChange = (rating: string) => {
    const newRating = selectedRating === rating ? '' : rating;
    setSelectedRating(newRating);
    updateURL({ rating: newRating || undefined });
  };

  const handlePriceChange = (values: number[]) => {
    setPriceRange(values);
    updateURL({ 
      minPrice: values[0].toString(), 
      maxPrice: values[1].toString() 
    });
  };

  const clearAllFilters = () => {
    setPriceRange([0, 5000]);
    setSelectedBrands([]);
    setSelectedRating('');
    setInStock(false);
    router.push(window.location.pathname);
  };

  const activeFiltersCount = selectedBrands.length + 
    (selectedRating ? 1 : 0) + 
    (inStock ? 1 : 0) + 
    (priceRange[0] > 0 || priceRange[1] < 5000 ? 1 : 0);

  return (
    <div className="space-y-6">
      {/* Filter Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Filter className="w-5 h-5" />
              {t('filters')}
            </CardTitle>
            {activeFiltersCount > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {activeFiltersCount}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="w-4 h-4 mr-1" />
                  {t('clear')}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Sort Options */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">{t('sortBy')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {sortOptions.map((option) => (
            <Button
              key={option.value}
              variant={currentSort === option.value ? "default" : "ghost"}
              size="sm"
              className="w-full justify-start"
              onClick={() => handleSortChange(option.value)}
            >
              {option.label}
            </Button>
          ))}
        </CardContent>
      </Card>

      {/* Price Range */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">{t('priceRange')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Slider
              value={priceRange}
              onValueChange={handlePriceChange}
              max={5000}
              step={50}
              className="w-full"
            />
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>${priceRange[0]}</span>
              <span>${priceRange[1]}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Brands */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">{t('brands')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {brandOptions.map((brand) => (
            <div key={brand.value} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={brand.value}
                  checked={selectedBrands.includes(brand.value)}
                  onCheckedChange={(checked) => 
                    handleBrandChange(brand.value, checked as boolean)
                  }
                />
                <label
                  htmlFor={brand.value}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {brand.label}
                </label>
              </div>
              <span className="text-xs text-gray-500">({brand.count})</span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Rating */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">{t('rating')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {ratingOptions.map((rating) => (
            <Button
              key={rating.value}
              variant={selectedRating === rating.value ? "default" : "ghost"}
              size="sm"
              className="w-full justify-between"
              onClick={() => handleRatingChange(rating.value)}
            >
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 fill-current" />
                <span>{rating.label}</span>
              </div>
              <span className="text-xs">({rating.count})</span>
            </Button>
          ))}
        </CardContent>
      </Card>

      {/* Availability */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">{t('availability')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="in-stock"
              checked={inStock}
              onCheckedChange={(checked) => setInStock(checked as boolean)}
            />
            <label
              htmlFor="in-stock"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {t('inStock')}
            </label>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
