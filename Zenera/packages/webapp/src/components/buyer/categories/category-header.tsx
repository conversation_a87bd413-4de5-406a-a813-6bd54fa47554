"use client";

import { Category } from '@zenera/sharing';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Badge } from '@/components/ui/badge';
import { Package } from 'lucide-react';

interface CategoryHeaderProps {
  category: Category;
  totalProducts: number;
  locale: string;
}

export function CategoryHeader({ category, totalProducts, locale }: CategoryHeaderProps) {
  const { t } = useZeneraTranslation('categories');

  return (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-white mt-6">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%227%22%20cy%3D%227%22%20r%3D%227%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>
      
      <div className="relative z-10">
        <div className="flex flex-col lg:flex-row lg:items-center gap-6">
          {/* Category Image */}
          {category.image && (
            <div className="flex-shrink-0">
              <div className="w-24 h-24 lg:w-32 lg:h-32 rounded-2xl overflow-hidden bg-white/10 backdrop-blur-sm border border-white/20">
                <img
                  src={category.image}
                  alt={category.name}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          )}

          {/* Category Info */}
          <div className="flex-grow">
            <div className="flex items-center gap-3 mb-3">
              <Package className="w-8 h-8" />
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                {t('category')}
              </Badge>
            </div>
            
            <h1 className="text-3xl lg:text-4xl font-bold mb-3">
              {category.name}
            </h1>
            
            {category.description && (
              <p className="text-lg text-white/90 mb-4 max-w-2xl">
                {category.description}
              </p>
            )}

            <div className="flex flex-wrap items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span>{totalProducts} {t('products')}</span>
              </div>
              
              {category.children && category.children.length > 0 && (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                  <span>{category.children.length} {t('subcategories')}</span>
                </div>
              )}
              
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>{t('active')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-4 left-4 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>
    </div>
  );
}
