"use client";

import Link from 'next/link';
import { Category } from '@zenera/sharing';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight } from 'lucide-react';

interface CategorySubcategoriesProps {
  subcategories: Category[];
  locale: string;
}

export function CategorySubcategories({ subcategories, locale }: CategorySubcategoriesProps) {
  const { t } = useZeneraTranslation('categories');

  if (!subcategories || subcategories.length === 0) {
    return null;
  }

  return (
    <div className="mt-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">
        {t('subcategories')}
      </h2>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {subcategories.map((subcategory) => (
          <Link
            key={subcategory.id}
            href={`/${locale}/categories/${subcategory.id}`}
            className="group"
          >
            <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border-0 bg-white">
              <CardContent className="p-0">
                {/* Image */}
                <div className="relative overflow-hidden rounded-t-lg">
                  <div className="aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200">
                    {subcategory.image ? (
                      <img
                        src={subcategory.image}
                        alt={subcategory.name}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
                        <div className="w-16 h-16 bg-blue-200 rounded-full flex items-center justify-center">
                          <span className="text-2xl font-bold text-blue-600">
                            {subcategory.name.charAt(0)}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300"></div>
                  
                  {/* Arrow Icon */}
                  <div className="absolute top-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <ArrowRight className="w-4 h-4 text-gray-700" />
                  </div>
                </div>

                {/* Content */}
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                    {subcategory.name}
                  </h3>
                  
                  {subcategory.description && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {subcategory.description}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
