"use client";

import { useState } from 'react';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { User, Settings, Shield, Bell } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { AccountHeader } from './account-header';
import { PersonalInfoTab } from './personal-info-tab';
import { SecurityTab } from './security-tab';
import { PreferencesTab } from './preferences-tab';
import { AddressesTab } from './addresses-tab';

interface AccountPageProps {
  locale: string;
}

export function AccountPage({ locale }: AccountPageProps) {
  const { t } = useZeneraTranslation('account');
  const [activeTab, setActiveTab] = useState('personal');

  const breadcrumbItems = [
    { label: t('common.home', 'Home'), href: `/${locale}` },
    { label: t('title'), href: `/${locale}/account` }
  ];

  const tabs = [
    {
      id: 'personal',
      label: t('personalInfo'),
      icon: User,
      component: PersonalInfoTab
    },
    {
      id: 'addresses',
      label: t('addresses'),
      icon: Settings,
      component: AddressesTab
    },
    {
      id: 'security',
      label: t('security'),
      icon: Shield,
      component: SecurityTab
    },
    {
      id: 'preferences',
      label: t('preferences'),
      icon: Bell,
      component: PreferencesTab
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-6">
        <SimpleBreadcrumb items={breadcrumbItems} />
      </div>

      {/* Page Content */}
      <div className="container mx-auto px-4 py-8">
        <AccountHeader locale={locale} />

        {/* Account Tabs */}
        <div className="mt-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            {/* Tab Navigation */}
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 bg-white/80 backdrop-blur-sm border-0 shadow-lg p-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex items-center gap-2 data-[state=active]:bg-blue-600 data-[state=active]:text-white"
                  >
                    <Icon className="w-4 h-4" />
                    <span className="hidden sm:inline">{tab.label}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {/* Tab Content */}
            {tabs.map((tab) => {
              const Component = tab.component;
              return (
                <TabsContent key={tab.id} value={tab.id} className="space-y-6">
                  <Component locale={locale} />
                </TabsContent>
              );
            })}
          </Tabs>
        </div>
      </div>
    </div>
  );
}
