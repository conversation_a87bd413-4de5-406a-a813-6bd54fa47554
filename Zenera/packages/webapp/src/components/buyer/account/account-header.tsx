"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { User as UserIcon, Camera, Star, ShoppingBag, Heart } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import type { User } from '@zenera/sharing';
import { Role } from '@zenera/sharing';

interface AccountHeaderProps {
  locale: string;
}

// Mock user data using proper User type
const mockUser: User = {
  id: 'user-1',
  email: '<EMAIL>',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  phone_number: '+****************',
  roles: [Role.CUSTOMER],
  is_active: true,
  is_verified: true,
  avatar_url: '',
  last_login_at: new Date('2024-01-15'),
  customer_info: {
    points: 1250,
    addresses: ['address-1', 'address-2'],
    default_address: 'address-1'
  },
  created_at: new Date('2023-01-15'),
  updated_at: new Date('2024-01-15')
};

// Additional stats that would come from API
const mockUserStats = {
  totalOrders: 24,
  totalSpent: 2450.50,
  wishlistItems: 12,
  membershipTier: 'Gold'
};

export function AccountHeader({ locale }: AccountHeaderProps) {
  const { t } = useZeneraTranslation('account');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale === 'vi' ? 'vi-VN' : 'en-US', {
      year: 'numeric',
      month: 'long'
    });
  };

  const getTierColor = (tier: string) => {
    switch (tier.toLowerCase()) {
      case 'gold':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'silver':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'platinum':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row lg:items-center gap-6">
          {/* Avatar and Basic Info */}
          <div className="flex items-center gap-4">
            <div className="relative">
              <Avatar className="w-20 h-20 lg:w-24 lg:h-24">
                <AvatarImage src={mockUser.avatar_url || ''} alt={`${mockUser.first_name} ${mockUser.last_name}`} />
                <AvatarFallback className="bg-blue-100 text-blue-600 text-xl font-semibold">
                  {mockUser.first_name[0]}{mockUser.last_name[0]}
                </AvatarFallback>
              </Avatar>
              <Button
                size="sm"
                variant="outline"
                className="absolute -bottom-1 -right-1 w-8 h-8 rounded-full p-0 bg-white shadow-md"
              >
                <Camera className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-1">
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
                {mockUser.first_name} {mockUser.last_name}
              </h1>
              <p className="text-gray-600">{mockUser.email}</p>
              <div className="flex items-center gap-2">
                <Badge className={getTierColor(mockUserStats.membershipTier)}>
                  <Star className="w-3 h-3 mr-1" />
                  {mockUserStats.membershipTier} Member
                </Badge>
                <span className="text-sm text-gray-500">
                  Member since {formatDate(mockUser.created_at?.toISOString().split('T')[0] || '')}
                </span>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="flex-1 grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <ShoppingBag className="w-6 h-6 text-blue-600 mx-auto mb-1" />
              <div className="text-2xl font-bold text-blue-600">{mockUserStats.totalOrders}</div>
              <div className="text-xs text-gray-600">Total Orders</div>
            </div>

            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">${mockUserStats.totalSpent.toLocaleString()}</div>
              <div className="text-xs text-gray-600">Total Spent</div>
            </div>

            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <Heart className="w-6 h-6 text-purple-600 mx-auto mb-1" />
              <div className="text-2xl font-bold text-purple-600">{mockUserStats.wishlistItems}</div>
              <div className="text-xs text-gray-600">Wishlist Items</div>
            </div>

            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <Star className="w-6 h-6 text-yellow-600 mx-auto mb-1" />
              <div className="text-2xl font-bold text-yellow-600">{mockUser.customer_info?.points.toLocaleString() || '0'}</div>
              <div className="text-xs text-gray-600">Loyalty Points</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
