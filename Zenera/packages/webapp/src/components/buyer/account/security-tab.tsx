"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Shield, Key, Smartphone } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface SecurityTabProps {
  locale: string;
}

export function SecurityTab({ locale }: SecurityTabProps) {
  const { t } = useZeneraTranslation('account');

  return (
    <div className="space-y-6">
      {/* Change Password */}
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="w-5 h-5" />
            {t('changePassword')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Button variant="outline" className="w-full sm:w-auto">
            {t('changePassword')}
          </But<PERSON>>
        </CardContent>
      </Card>

      {/* Two-Factor Authentication */}
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="w-5 h-5" />
            {t('twoFactorAuth')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">{t('enableTwoFactor')}</p>
              <p className="text-sm text-gray-600">{t('twoFactorDisabled')}</p>
            </div>
            <Switch />
          </div>
        </CardContent>
      </Card>

      {/* Account Activity */}
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            {t('accountActivity')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Button variant="outline" className="w-full sm:w-auto">
              {t('loginHistory')}
            </Button>
            <Button variant="outline" className="w-full sm:w-auto">
              {t('deviceManagement')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
