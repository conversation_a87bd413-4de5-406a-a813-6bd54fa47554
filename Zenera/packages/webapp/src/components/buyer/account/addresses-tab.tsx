"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MapPin, Plus } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface AddressesTabProps {
  locale: string;
}

export function AddressesTab({ locale }: AddressesTabProps) {
  const { t } = useZeneraTranslation('account');

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            {t('addresses')}
          </CardTitle>
          <Button className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4" />
            {t('addAddress')}
          </Button>
        </div>
      </<PERSON><PERSON><PERSON>er>
      <CardContent>
        <div className="text-center py-12">
          <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No addresses yet</h3>
          <p className="text-gray-600 mb-4">Add your first address to get started</p>
          <Button className="bg-blue-600 hover:bg-blue-700">
            {t('addAddress')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
