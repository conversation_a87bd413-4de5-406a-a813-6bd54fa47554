"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, Heart } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import type { Product } from '@zenera/sharing';
import Link from 'next/link';

interface RelatedProductsProps {
  locale: string;
}

// Mock related products data
const mockRelatedProducts: Product[] = [
  {
    id: 'related-1',
    name: 'Wireless Mouse',
    slug: 'wireless-mouse',
    description: 'Ergonomic wireless mouse with precision tracking',
    base_price: 49.99,
    compare_at_price: 39.99,
    images: ['https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop'],
    category_id: 'cat-1',
    is_active: true,
    tags: ['mouse', 'wireless'],
    attributes: [],
    avg_rating: 4.5,
    review_count: 25,
    created_at: new Date('2024-01-20T10:30:00Z'),
    updated_at: new Date('2024-01-22T14:45:00Z')
  },
  {
    id: 'related-2',
    name: 'Mechanical Keyboard',
    slug: 'mechanical-keyboard',
    description: 'RGB backlit mechanical keyboard for gaming',
    base_price: 129.99,
    images: ['https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=400&fit=crop'],
    category_id: 'cat-1',
    is_active: true,
    tags: ['keyboard', 'gaming'],
    attributes: [],
    avg_rating: 4.8,
    review_count: 15,
    created_at: new Date('2024-01-18T09:15:00Z'),
    updated_at: new Date('2024-01-20T16:20:00Z')
  },
  {
    id: 'related-3',
    name: 'USB-C Hub',
    slug: 'usb-c-hub',
    description: 'Multi-port USB-C hub with HDMI and charging',
    base_price: 79.99,
    compare_at_price: 59.99,
    images: ['https://images.unsplash.com/photo-1625842268584-8f3296236761?w=400&h=400&fit=crop'],
    category_id: 'cat-1',
    is_active: true,
    tags: ['hub', 'usb-c'],
    attributes: [],
    avg_rating: 4.2,
    review_count: 30,
    created_at: new Date('2024-01-15T14:20:00Z'),
    updated_at: new Date('2024-01-18T11:30:00Z')
  },
  {
    id: 'related-4',
    name: 'Laptop Stand',
    slug: 'laptop-stand',
    description: 'Adjustable aluminum laptop stand for better ergonomics',
    base_price: 89.99,
    images: ['https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop'],
    category_id: 'cat-1',
    is_active: true,
    tags: ['stand', 'laptop'],
    attributes: [],
    avg_rating: 4.6,
    review_count: 20,
    created_at: new Date('2024-01-12T08:45:00Z'),
    updated_at: new Date('2024-01-15T13:20:00Z')
  }
];

export function RelatedProducts({ locale }: RelatedProductsProps) {
  const { t } = useZeneraTranslation('wishlist');

  const handleAddToCart = (productId: string) => {
    console.log('Adding to cart:', productId);
    // Add to cart logic
  };

  const handleAddToWishlist = (productId: string) => {
    console.log('Adding to wishlist:', productId);
    // Add to wishlist logic
  };

  return (
    <div className="mt-16">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{t('youMayLike', 'You May Also Like')}</h2>
          <p className="text-gray-600 mt-1">{t('relatedProductsDesc', 'Discover more products you might love')}</p>
        </div>
        <Link href={`/${locale}/products`}>
          <Button variant="outline">{t('viewAll', 'View All')}</Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {mockRelatedProducts.map((product) => (
          <Card key={product.id} className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-4">
              <div className="space-y-3">
                {/* Product Image */}
                <div className="relative">
                  <Link href={`/${locale}/products/${product.id}`}>
                    <img
                      src={product.images[0]}
                      alt={product.name}
                      className="w-full h-48 object-cover rounded-lg group-hover:scale-105 transition-transform duration-300"
                    />
                  </Link>
                  {product.compare_at_price && product.base_price && product.compare_at_price > product.base_price && (
                    <Badge className="absolute top-2 left-2 bg-red-500 text-white">
                      {Math.round(((product.compare_at_price - product.base_price) / product.compare_at_price) * 100)}% OFF
                    </Badge>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => product.id && handleAddToWishlist(product.id)}
                    className="absolute top-2 right-2 w-8 h-8 p-0 bg-white/80 backdrop-blur-sm hover:bg-white"
                  >
                    <Heart className="w-4 h-4" />
                  </Button>
                </div>

                {/* Product Info */}
                <div className="space-y-2">
                  <Link href={`/${locale}/products/${product.id}`}>
                    <h3 className="font-medium text-gray-900 hover:text-blue-600 transition-colors truncate">
                      {product.name}
                    </h3>
                  </Link>
                  <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>
                  
                  <div className="flex items-center gap-2">
                    {product.compare_at_price && product.base_price && product.compare_at_price > product.base_price ? (
                      <>
                        <span className="text-lg font-bold text-red-600">${product.base_price}</span>
                        <span className="text-sm text-gray-500 line-through">${product.compare_at_price}</span>
                      </>
                    ) : (
                      <span className="text-lg font-bold text-gray-900">${product.base_price || 0}</span>
                    )}
                  </div>

                  {/* Add to Cart Button */}
                  <Button
                    onClick={() => product.id && handleAddToCart(product.id)}
                    className="w-full flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                  >
                    <ShoppingCart className="w-4 h-4" />
                    {t('addToCart', 'Add to Cart')}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
