"use client";

import { useState } from 'react';
import { SimpleBreadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Heart, Grid, List, Share, Trash2 } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import { WishlistHeader } from './wishlist-header';
import { WishlistGrid } from './wishlist-grid';
import { EmptyWishlist } from './empty-wishlist';
import { RelatedProducts } from './related-products';
import type { Product, Wishlist, WishlistItem } from '@zenera/sharing';
import { WishlistPrivacy } from '@zenera/sharing';

interface WishlistPageProps {
  locale: string;
}

// Mock products data
const mockProducts: Product[] = [
  {
    id: 'prod-1',
    name: 'Wireless Bluetooth Headphones',
    description: 'Premium quality wireless headphones with noise cancellation',
    price: 199.99,
    sale_price: 149.99,
    currency: 'USD',
    images: ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop'],
    category_id: 'cat-1',
    shop_id: 'shop-1',
    stock_quantity: 50,
    is_active: true,
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:45:00Z'
  },
  {
    id: 'prod-2',
    name: 'Smart Fitness Watch',
    description: 'Advanced fitness tracking with heart rate monitor',
    price: 299.99,
    currency: 'USD',
    images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop'],
    category_id: 'cat-2',
    shop_id: 'shop-2',
    stock_quantity: 0,
    is_active: true,
    created_at: '2024-01-10T09:15:00Z',
    updated_at: '2024-01-12T16:20:00Z'
  },
  {
    id: 'prod-3',
    name: 'Premium Coffee Maker',
    description: 'Professional grade coffee maker for home use',
    price: 449.99,
    currency: 'USD',
    images: ['https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=400&fit=crop'],
    category_id: 'cat-3',
    shop_id: 'shop-1',
    stock_quantity: 25,
    is_active: true,
    created_at: '2024-01-05T14:20:00Z',
    updated_at: '2024-01-08T11:30:00Z'
  }
];

// Mock wishlist data using proper Wishlist type
const mockWishlist: Wishlist = {
  id: 'wishlist-1',
  user_id: 'user-1',
  name: 'My Wishlist',
  description: 'Items I want to buy',
  privacy: WishlistPrivacy.PRIVATE,
  is_default: true,
  items: [
    {
      id: 'wishlist-item-1',
      product_id: 'prod-1',
      added_at: new Date('2024-01-15T10:30:00Z'),
      notes: 'Great for work',
      priority: 1
    },
    {
      id: 'wishlist-item-2',
      product_id: 'prod-2',
      added_at: new Date('2024-01-10T09:15:00Z'),
      priority: 2
    },
    {
      id: 'wishlist-item-3',
      product_id: 'prod-3',
      added_at: new Date('2024-01-05T14:20:00Z'),
      notes: 'For the kitchen',
      priority: 3
    }
  ],
  created_at: new Date('2024-01-01T00:00:00Z'),
  updated_at: new Date('2024-01-15T10:30:00Z')
};

// Helper function to get product by ID
const getProductById = (productId: string): Product | undefined => {
  return mockProducts.find(product => product.id === productId);
};

// Get wishlist items with product details
const getWishlistItemsWithProducts = (): (WishlistItem & { product: Product })[] => {
  return mockWishlist.items
    .map(item => {
      const product = getProductById(item.product_id);
      return product ? { ...item, product } : null;
    })
    .filter(Boolean) as (WishlistItem & { product: Product })[];
};

export function WishlistPage({ locale }: WishlistPageProps) {
  const { t } = useZeneraTranslation('wishlist');
  const [wishlistItemsWithProducts, setWishlistItemsWithProducts] = useState(getWishlistItemsWithProducts());
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('dateAdded');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  const breadcrumbItems = [
    { label: t('common.home', 'Home'), href: `/${locale}` },
    { label: t('title'), href: `/${locale}/wishlist` }
  ];

  const handleRemoveItem = (productId: string) => {
    setWishlistItemsWithProducts(prev => prev.filter(item => item.product.id !== productId));
    setSelectedItems(prev => prev.filter(id => id !== productId));
  };

  const handleAddToCart = (productId: string) => {
    // Mock add to cart functionality
    console.log('Adding to cart:', productId);
    // Show success message
  };

  const handleMoveToCart = (productId: string) => {
    handleAddToCart(productId);
    handleRemoveItem(productId);
  };

  const handleSelectItem = (productId: string, selected: boolean) => {
    if (selected) {
      setSelectedItems(prev => [...prev, productId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== productId));
    }
  };

  const handleSelectAll = () => {
    if (selectedItems.length === wishlistItemsWithProducts.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(wishlistItemsWithProducts.map(item => item.product.id));
    }
  };

  const handleBulkRemove = () => {
    setWishlistItemsWithProducts(prev => prev.filter(item => !selectedItems.includes(item.product.id)));
    setSelectedItems([]);
  };

  const handleBulkAddToCart = () => {
    selectedItems.forEach(productId => {
      handleAddToCart(productId);
    });
    setSelectedItems([]);
  };

  const handleSort = (sortValue: string) => {
    setSortBy(sortValue);
    const sorted = [...wishlistItemsWithProducts].sort((a, b) => {
      switch (sortValue) {
        case 'priceHighToLow':
          return (b.product.sale_price || b.product.price) - (a.product.sale_price || a.product.price);
        case 'priceLowToHigh':
          return (a.product.sale_price || a.product.price) - (b.product.sale_price || b.product.price);
        case 'alphabetical':
          return a.product.name.localeCompare(b.product.name);
        case 'dateAdded':
        default:
          return new Date(b.added_at).getTime() - new Date(a.added_at).getTime();
      }
    });
    setWishlistItemsWithProducts(sorted);
  };

  const totalValue = wishlistItemsWithProducts.reduce((sum, item) => sum + (item.product.sale_price || item.product.price), 0);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="container mx-auto px-4 pt-6">
        <SimpleBreadcrumb items={breadcrumbItems} />
      </div>

      {/* Page Content */}
      <div className="container mx-auto px-4 py-8">
        <WishlistHeader
          locale={locale}
          totalItems={wishlistItemsWithProducts.length}
          totalValue={totalValue}
        />

        {wishlistItemsWithProducts.length === 0 ? (
          <EmptyWishlist locale={locale} />
        ) : (
          <div className="space-y-6">
            {/* Controls */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex items-center gap-4">
                {/* Bulk Actions */}
                {selectedItems.length > 0 && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      {t('selectedItems', { count: selectedItems.length })}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleBulkAddToCart}
                      className="flex items-center gap-1"
                    >
                      {t('addSelectedToCart')}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleBulkRemove}
                      className="flex items-center gap-1 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                      {t('removeSelected')}
                    </Button>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-4">
                {/* Sort */}
                <Select value={sortBy} onValueChange={handleSort}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder={t('sortBy')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="dateAdded">{t('dateAdded')}</SelectItem>
                    <SelectItem value="priceHighToLow">{t('priceHighToLow')}</SelectItem>
                    <SelectItem value="priceLowToHigh">{t('priceLowToHigh')}</SelectItem>
                    <SelectItem value="alphabetical">{t('alphabetical')}</SelectItem>
                  </SelectContent>
                </Select>

                {/* View Mode */}
                <div className="flex items-center gap-1 bg-white rounded-lg p-1 shadow-sm">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="p-2"
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="p-2"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>

                {/* Share */}
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Share className="w-4 h-4" />
                  {t('shareWishlist')}
                </Button>
              </div>
            </div>

            {/* Wishlist Grid */}
            <WishlistGrid
              items={wishlistItemsWithProducts.map(item => item.product)}
              viewMode={viewMode}
              selectedItems={selectedItems}
              onSelectItem={handleSelectItem}
              onSelectAll={handleSelectAll}
              onRemoveItem={handleRemoveItem}
              onAddToCart={handleAddToCart}
              onMoveToCart={handleMoveToCart}
              locale={locale}
            />
          </div>
        )}

        {/* Related Products Section */}
        <RelatedProducts locale={locale} />
      </div>
    </div>
  );
}
