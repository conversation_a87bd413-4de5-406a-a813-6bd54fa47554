"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Heart, ShoppingCart, Trash2, Eye } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';
import type { Product } from '@zenera/sharing';

interface WishlistGridProps {
  items: Product[];
  viewMode: 'grid' | 'list';
  selectedItems: string[];
  onSelectItem: (productId: string, selected: boolean) => void;
  onSelectAll: () => void;
  onRemoveItem: (productId: string) => void;
  onAddToCart: (productId: string) => void;
  onMoveToCart: (productId: string) => void;
  locale: string;
}

export function WishlistGrid({
  items,
  viewMode,
  selectedItems,
  onSelectItem,
  onSelectAll,
  onRemoveItem,
  onAddToCart,
  onMoveToCart,
  locale
}: WishlistGridProps) {
  const { t } = useZeneraTranslation('wishlist');

  const isAllSelected = selectedItems.length === items.length && items.length > 0;

  return (
    <div className="space-y-4">
      {/* Select All */}
      <div className="flex items-center gap-2">
        <Checkbox
          checked={isAllSelected}
          onCheckedChange={onSelectAll}
        />
        <span className="text-sm text-gray-600">{t('selectAll')}</span>
      </div>

      {/* Grid/List */}
      <div className={viewMode === 'grid' 
        ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
        : 'space-y-4'
      }>
        {items.map((item) => (
          <Card key={item.id} className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-shadow group">
            <CardContent className="p-4">
              {viewMode === 'grid' ? (
                // Grid Layout
                <div className="space-y-3">
                  {/* Checkbox */}
                  <div className="flex justify-between items-start">
                    <Checkbox
                      checked={selectedItems.includes(item.id)}
                      onCheckedChange={(checked) => onSelectItem(item.id, checked === true)}
                    />
                  </div>

                  {/* Product Image */}
                  <div className="relative">
                    <img
                      src={item.images[0]}
                      alt={item.name}
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    {item.sale_price && (
                      <Badge className="absolute top-2 left-2 bg-red-500 text-white">
                        {t('discount', { percent: Math.round(((item.price - item.sale_price) / item.price) * 100) })}
                      </Badge>
                    )}
                    {item.stock_quantity === 0 && (
                      <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                        <span className="text-white text-sm font-medium">{t('outOfStock')}</span>
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-900 truncate">{item.name}</h3>
                    <p className="text-sm text-gray-600 line-clamp-2">{item.description}</p>

                    <div className="flex items-center gap-2">
                      {item.sale_price ? (
                        <>
                          <span className="text-lg font-bold text-red-600">${item.sale_price}</span>
                          <span className="text-sm text-gray-500 line-through">${item.price}</span>
                        </>
                      ) : (
                        <span className="text-lg font-bold text-gray-900">${item.price}</span>
                      )}
                    </div>

                    {/* Stock Status */}
                    <div>
                      {item.stock_quantity === 0 ? (
                        <Badge variant="secondary" className="bg-red-100 text-red-800">
                          {t('outOfStock')}
                        </Badge>
                      ) : item.stock_quantity < 10 ? (
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          {t('limitedStock')}
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {t('inStock')}
                        </Badge>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={() => onAddToCart(item.id)}
                        disabled={item.stock_quantity === 0}
                        className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 flex-1"
                      >
                        <ShoppingCart className="w-4 h-4" />
                        {t('addToCart')}
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onRemoveItem(item.id)}
                        className="flex items-center gap-1 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                // List Layout
                <div className="flex items-start gap-3">
                  {/* Checkbox */}
                  <Checkbox
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={(checked) => onSelectItem(item.id, checked === true)}
                  />

                  {/* Product Image */}
                  <div className="relative flex-shrink-0">
                    <img
                      src={item.images[0]}
                      alt={item.name}
                      className="w-20 h-20 object-cover rounded-lg"
                    />
                    {item.sale_price && (
                      <Badge className="absolute top-1 left-1 bg-red-500 text-white text-xs">
                        {Math.round(((item.price - item.sale_price) / item.price) * 100)}%
                      </Badge>
                    )}
                    {item.stock_quantity === 0 && (
                      <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                        <span className="text-white text-xs font-medium">{t('outOfStock')}</span>
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="flex-1 min-w-0 space-y-2">
                    <h3 className="font-medium text-gray-900 truncate">{item.name}</h3>
                    <p className="text-sm text-gray-600 line-clamp-2">{item.description}</p>

                    <div className="flex items-center gap-2">
                      {item.sale_price ? (
                        <>
                          <span className="text-lg font-bold text-red-600">${item.sale_price}</span>
                          <span className="text-sm text-gray-500 line-through">${item.price}</span>
                        </>
                      ) : (
                        <span className="text-lg font-bold text-gray-900">${item.price}</span>
                      )}
                    </div>

                    {/* Stock Status */}
                    <div>
                      {item.stock_quantity === 0 ? (
                        <Badge variant="secondary" className="bg-red-100 text-red-800">
                          {t('outOfStock')}
                        </Badge>
                      ) : item.stock_quantity < 10 ? (
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          {t('limitedStock')}
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {t('inStock')}
                        </Badge>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={() => onAddToCart(item.id)}
                        disabled={item.stock_quantity === 0}
                        className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700"
                      >
                        <ShoppingCart className="w-4 h-4" />
                        {t('addToCart')}
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onRemoveItem(item.id)}
                        className="flex items-center gap-1 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
