"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Heart, ArrowRight } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface EmptyWishlistProps {
  locale: string;
}

export function EmptyWishlist({ locale }: EmptyWishlistProps) {
  const { t } = useZeneraTranslation('wishlist');

  return (
    <Card className="max-w-2xl mx-auto bg-white/80 backdrop-blur-sm border-0 shadow-xl">
      <CardContent className="p-6 sm:p-12 text-center">
        {/* Empty Wishlist Icon */}
        <div className="relative mb-6 sm:mb-8">
          <div className="w-24 h-24 sm:w-32 sm:h-32 mx-auto bg-gradient-to-br from-pink-100 to-red-100 rounded-full flex items-center justify-center">
            <Heart className="w-12 h-12 sm:w-16 sm:h-16 text-pink-600" />
          </div>
          <div className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 w-6 h-6 sm:w-8 sm:h-8 bg-gray-400 rounded-full flex items-center justify-center">
            <span className="text-white text-xs sm:text-sm font-bold">0</span>
          </div>
        </div>

        {/* Empty State Content */}
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
          {t('emptyTitle')}
        </h2>
        
        <p className="text-gray-600 text-base sm:text-lg mb-8 max-w-md mx-auto">
          {t('emptyDescription')}
        </p>

        {/* Primary CTA */}
        <Link href={`/${locale}/products`}>
          <Button size="lg" className="bg-pink-600 hover:bg-pink-700 text-white px-6 sm:px-8 py-3 text-base sm:text-lg font-medium mb-8 w-full sm:w-auto">
            {t('startShopping')}
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </Link>

        {/* Help Text */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Tip: Click the heart icon on any product to add it to your wishlist
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
