"use client";

import { Heart } from 'lucide-react';
import { useZeneraTranslation } from '@/lib/hooks/use-translation';

interface WishlistHeaderProps {
  locale: string;
  totalItems: number;
  totalValue: number;
}

export function WishlistHeader({ locale, totalItems, totalValue }: WishlistHeaderProps) {
  const { t } = useZeneraTranslation('wishlist');

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-pink-100 rounded-xl">
          <Heart className="w-8 h-8 text-pink-600" />
        </div>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            {t('title')}
          </h1>
          <p className="text-gray-600 mt-1">
            {t('totalItems', { count: totalItems })} • {t('totalValue', { amount: totalValue.toFixed(2) })}
          </p>
        </div>
      </div>
    </div>
  );
}
