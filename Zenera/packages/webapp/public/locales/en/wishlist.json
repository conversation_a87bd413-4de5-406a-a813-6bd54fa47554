{"title": "My Wishlist", "subtitle": "Save your favorite items for later", "emptyTitle": "Your wishlist is empty", "emptyDescription": "Start adding products you love to your wishlist", "startShopping": "Start Shopping", "addToCart": "Add to Cart", "removeFromWishlist": "Remove from Wishlist", "moveToCart": "Move to Cart", "shareWishlist": "Share Wishlist", "clearWishlist": "Clear Wishlist", "sortBy": "Sort by", "dateAdded": "Date Added", "priceHighToLow": "Price: High to Low", "priceLowToHigh": "Price: Low to High", "alphabetical": "Alphabetical", "availability": "Availability", "inStock": "In Stock", "outOfStock": "Out of Stock", "limitedStock": "Limited Stock", "onSale": "On Sale", "newArrival": "New Arrival", "price": "Price", "originalPrice": "Original Price", "salePrice": "Sale Price", "discount": "{{percent}}% off", "freeShipping": "Free Shipping", "quickView": "Quick View", "compare": "Compare", "addedToCart": "Added to cart successfully", "removedFromWishlist": "Removed from wishlist", "movedToCart": "Moved to cart successfully", "shareTitle": "Check out my wishlist", "shareDescription": "I've curated a collection of amazing products on Zenera", "copyLink": "Copy Link", "linkCopied": "Link copied to clipboard", "emailWishlist": "<PERSON>ail <PERSON>", "printWishlist": "Print Wishlist", "totalItems": "{{count}} items", "totalValue": "Total value: ${{amount}}", "averagePrice": "Average price: ${{amount}}", "filters": "Filters", "category": "Category", "priceRange": "Price Range", "brand": "Brand", "rating": "Rating", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters", "viewMode": "View Mode", "gridView": "Grid View", "listView": "List View", "itemsPerPage": "Items per page", "showingResults": "Showing {{start}}-{{end}} of {{total}} items", "loadMore": "Load More", "selectAll": "Select All", "selectedItems": "{{count}} items selected", "bulkActions": "Bulk Actions", "addSelectedToCart": "Add Selected to Cart", "removeSelected": "Remove Selected", "createList": "Create New List", "listName": "List Name", "listDescription": "List Description", "createListSuccess": "List created successfully", "publicList": "Make this list public", "privateList": "Keep this list private", "recentlyViewed": "Recently Viewed", "recommendations": "You Might Also Like", "similarItems": "Similar Items", "backInStock": "Back in Stock", "priceDropAlert": "Price Drop Alert", "notifyWhenAvailable": "Notify when available", "notifyOnPriceDrop": "Notify on price drop", "notifications": "Notifications", "notificationSettings": "Notification Settings", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications"}